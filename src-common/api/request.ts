import { createFetch,  } from 'ofetch'
import type { FetchResponse } from 'ofetch';
import { MessagePlugin } from 'tdesign-vue-next'

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

/**
 * 特殊错误类，用于标识已经在内部处理过的响应
 * 当请求拦截器内部已经处理了错误（如显示错误消息），
 * 抛出此错误可以阻止外部调用代码的进一步处理
 */
export class InternallyHandledError extends Error {
  constructor(message: string = '请求已在内部处理') {
    super(message);
    this.name = 'InternallyHandledError';
  }
}

export enum RequestMethod {
  GET = 'GET',
  POST = 'POST',
}

export interface IResponse<T> {
  code: number;
  data: T;
  msg: string;
}


function isJson(response: FetchResponse<any>): boolean {
  // 方法1: 检查 Content-Type 头部
  const contentType = response.headers.get('content-type');
  const isJsonByHeader = contentType && contentType.includes('application/json');
  if (isJsonByHeader) {
    return true;
  }
  return response._data && typeof response._data === 'object';
}
export const request = createFetch({
  defaults: {
    baseURL: BASE_URL,
    timeout: 5000,
    // 请求拦截器
    onRequest: async({ request, options }) => {
      console.log("[fetch request]", request, options);
      // 可以在这里添加认证 token 等
      // options.headers = { ...options.headers, Authorization: `Bearer ${token}` };
    },
    // 响应拦截器
    onResponse: async({ request, response, options }) => {
      if (isJson(response)) {
        const data = response._data as IResponse<any>;

        // 处理业务错误
        if (data.code !== 200) {
          if (data.code <= 999) {
            // 显示错误消息给用户
            await MessagePlugin.error(data.msg);
            // 抛出特殊错误，阻止外部 .then() 执行，
            // 外部 .catch() 可以通过检查错误类型来决定是否需要额外处理
            throw new InternallyHandledError(`请求失败: ${data.msg}`);
          }
          // code > 999 的错误不在内部处理，交给外部处理
        }

        // 成功响应，返回实际数据给外部处理
        return data.data;
      }
    },
    onResponseError: async({ request, response, options }) => {
      // Log error
      debugger
      console.log(
        "[fetch response error]",
        request,
        response.status,
        response.body
      );
    },
  }
})
