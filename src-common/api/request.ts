import { createFetch } from 'ofetch';
import type { FetchOptions } from 'ofetch';

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

export enum RequestMethod {
  GET = 'GET',
  POST = 'POST',
}

export interface IResponse<T> {
  code: number;
  data: T;
  msg: string;
}

export const request = createFetch({
  defaults: {
    baseURL: BASE_URL,
    timeout: 5000,
    // 请求拦截器
    onRequest: async({ request, options }) => {
      console.log("[fetch request]", request, options);
      // 可以在这里添加认证 token 等
      // options.headers = { ...options.headers, Authorization: `Bearer ${token}` };
    },
    // 响应拦截器
    onResponse: async({ request, response, options }) => {
      // 使用 response._data 获取已解析的响应体（推荐）
      console.log(response._data)
      
    },
  }
})
