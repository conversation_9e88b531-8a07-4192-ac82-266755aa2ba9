import { createFetch,  } from 'ofetch'
import type { FetchResponse } from 'ofetch';
import { MessagePlugin } from 'tdesign-vue-next'

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

export enum RequestMethod {
  GET = 'GET',
  POST = 'POST',
}

export interface IResponse<T> {
  code: number;
  data: T;
  msg: string;
}


function isJson(response: FetchResponse<any>): boolean {
  // 方法1: 检查 Content-Type 头部
  const contentType = response.headers.get('content-type');
  const isJsonByHeader = contentType && contentType.includes('application/json');
  if (isJsonByHeader) {
    return true;
  }
  return response._data && typeof response._data === 'object';
}
export const request = createFetch({
  defaults: {
    baseURL: BASE_URL,
    timeout: 5000,
    // 请求拦截器
    onRequest: async({ request, options }) => {
      console.log("[fetch request]", request, options);
      // 可以在这里添加认证 token 等
      // options.headers = { ...options.headers, Authorization: `Bearer ${token}` };
    },
    // 响应拦截器
    onResponse: async({ request, response, options }) => {
      if (isJson(response)) {
        const data = response._data as IResponse<any>;
        if (data.code !== 200) {
          if (data.code <= 999) {
            await MessagePlugin.error(data.msg);
          }
        }
        return data.data;
      }
    },
    onResponseError: async({ request, response, options }) => {
      // Log error
      debugger
      console.log(
        "[fetch response error]",
        request,
        response.status,
        response.body
      );
    },
  }
})
