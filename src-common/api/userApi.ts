import { request, InternallyHandledError } from '@src-common/api/request.ts'

export function getUserInfo() {
  request('/app/application/user/getList')
    .then(res => {
      console.log('✅ 外部处理成功响应:', res)
      // 这里可以处理成功的响应数据
    }).catch(err => {
      // 检查是否是内部已处理的错误
      if (err instanceof InternallyHandledError) {
        console.log('ℹ️ 请求已在内部处理，无需外部处理')
        return; // 不进行额外的错误处理
      }

      // 处理其他类型的错误（网络错误、服务器错误等）
      console.log('❌ 外部处理错误:', err)
    })
}

// 示例：返回 Promise 的函数，可以让调用者决定如何处理
export function getUserInfoAsync() {
  return request('/app/application/user/getList')
    .catch(err => {
      // 如果是内部已处理的错误，返回 null 或特殊值
      if (err instanceof InternallyHandledError) {
        return null; // 或者返回 { handled: true } 等特殊标识
      }
      // 重新抛出其他错误，让调用者处理
      throw err;
    });
}
